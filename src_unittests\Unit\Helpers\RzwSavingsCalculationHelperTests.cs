using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.DbModel;
using Xunit;

namespace RazeWinComTr.RewardTests.Unit.Helpers
{
    /// <summary>
    /// Unit tests for RZW Savings Calculation Helper
    /// Tests compound interest calculations and mathematical formulas
    /// </summary>
    public class RzwSavingsCalculationHelperTests
    {
        #region Compound Interest Calculation Tests

        [Theory]
        [InlineData(1000, 0.0003, 1, 0.3)] // Interest only: 1000 * (1 + 0.0003)^1 - 1000
        [InlineData(1000, 0.0003, 30, 9.039)] // Interest only: 1000 * (1 + 0.0003)^30 - 1000 = 9.039
        [InlineData(1000, 0.0003, 365, 115.702)] // Interest only: 1000 * (1 + 0.0003)^365 - 1000 = 115.702
        [InlineData(5000, 0.01, 1, 50)] // Interest only: 5000 * (1 + 0.01)^1 - 5000 = 50
        [InlineData(5000, 0.01, 12, 634.125)] // Interest only: 5000 * (1 + 0.01)^12 - 5000 = 634.125
        [InlineData(10000, 0.15, 1, 1500)] // Interest only: 10000 * (1 + 0.15)^1 - 10000
        public void CalculateCompoundInterest_ShouldReturnCorrectInterestAmount(decimal principal, decimal rate, int periods, decimal expectedInterest)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, periods);

            // Assert
            Assert.Equal(expectedInterest, Math.Round(result, 3, MidpointRounding.AwayFromZero));
        }

        [Theory]
        [InlineData(1000, 0.0003, 1, 0.3)] // Interest only
        [InlineData(1000, 0.0003, 30, 9.039)] // Interest only
        [InlineData(5000, 0.01, 12, 634.125)] // Interest only
        [InlineData(10000, 0.15, 1, 1500)] // Interest only
        public void CalculateCompoundInterest_ShouldReturnCorrectInterestOnly(decimal principal, decimal rate, int periods, decimal expectedInterest)
        {
            // Act
            var interestOnly = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, periods);

            // Assert
            Assert.Equal(expectedInterest, Math.Round(interestOnly, 3, MidpointRounding.AwayFromZero));
        }

        #endregion

        #region Daily Rate Calculation Tests

        [Theory]
        [InlineData(0.0003, "Daily", 0.0003)] // Daily rate stays same
        [InlineData(0.01, "Monthly", 0.000333333)] // Monthly to daily: 0.01/30
        [InlineData(0.15, "Yearly", 0.000410959)] // Yearly to daily: 0.15/365
        public void CalculateDailyRate_ShouldReturnCorrectDailyRate(decimal rate, string termType, decimal expectedDailyRate)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateDailyRate(rate, termType);

            // Assert
            Assert.Equal(expectedDailyRate, Math.Round(result, 9, MidpointRounding.AwayFromZero));
        }

        #endregion

        #region Final Amount Calculation Tests

        [Theory]
        [InlineData(1000, 0.0003, 1, 1000.3)] // 1000 * (1.0003)^1 = 1000.3
        [InlineData(1000, 0.0003, 30, 1009.039)] // 1000 * (1.0003)^30 = 1009.039
        [InlineData(5000, 0.000333333, 30, 5050.242)] // 5000 * (1.000333333)^30 = 5050.242
        [InlineData(10000, 0.000410959, 365, 11617.984)] // 10000 * (1.000410959)^365 = 11617.984
        public void CalculateFinalAmount_ShouldReturnCorrectAmount(decimal principal, decimal dailyRate, int days, decimal expectedAmount)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateFinalAmount(principal, dailyRate, days);

            // Assert - Allow for small rounding differences
            Assert.True(Math.Abs(result - expectedAmount) < 1m, $"Expected {expectedAmount}, got {result}");
        }

        #endregion

        #region Edge Cases and Validation Tests

        [Theory]
        [InlineData(0, 0.01, 30)] // Zero principal
        [InlineData(1000, 0, 30)] // Zero rate
        [InlineData(1000, 0.01, 0)] // Zero periods
        public void CalculateCompoundInterest_WithZeroValues_ShouldHandleGracefully(decimal principal, decimal rate, int periods)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, periods);

            // Assert - Should return 0 for zero cases (interest only)
            Assert.Equal(0, result);
        }

        [Theory]
        [InlineData(-1000, 0.01, 30)] // Negative principal
        [InlineData(1000, -0.01, 30)] // Negative rate
        [InlineData(1000, 0.01, -30)] // Negative periods
        public void CalculateCompoundInterest_WithNegativeValues_ShouldThrowOrHandleGracefully(decimal principal, decimal rate, int periods)
        {
            // Act & Assert
            if (principal < 0 || rate < 0 || periods < 0)
            {
                // Should either throw exception or return 0/principal
                var result = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, periods);
                // For negative values, we expect either an exception or a safe fallback
                Assert.True(result >= 0 || result == principal);
            }
        }

        [Fact]
        public void CalculateDailyRate_WithInvalidTermType_ShouldHandleGracefully()
        {
            // Arrange
            var invalidTermType = "Invalid";

            // Act
            var result = RzwSavingsCalculationHelper.CalculateDailyRate(0.01m, invalidTermType);

            // Assert
            // Should return the original rate or 0 for invalid term type
            Assert.True(result >= 0);
        }

        #endregion

        #region Precision and Rounding Tests

        [Theory]
        [InlineData(1000.12345678, 0.00030001, 1)] // High precision inputs
        [InlineData(999999.99999999, 0.00029999, 365)] // Large numbers with precision
        public void CalculateCompoundInterest_WithHighPrecision_ShouldMaintainAccuracy(decimal principal, decimal rate, int periods)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, periods);

            // Assert - result is interest only, not total amount
            Assert.True(result > 0); // Interest should be positive for positive rates
            Assert.True(result < principal); // Interest should be less than principal for reasonable rates
        }

        [Fact]
        public void CalculateCompoundInterest_ShouldRoundTo8DecimalPlaces()
        {
            // Arrange
            decimal principal = 1000m;
            decimal rate = 0.0003m;
            int periods = 1;

            // Act
            var result = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, periods);

            // Assert
            var decimalPlaces = BitConverter.GetBytes(decimal.GetBits(result)[3])[2];
            Assert.True(decimalPlaces <= 8, $"Result has {decimalPlaces} decimal places, expected <= 8");
        }

        #endregion

        #region Real-World Scenario Tests

        [Fact]
        public void DailyPlan_OneMonth_ShouldCalculateCorrectly()
        {
            // Arrange - Daily plan: 0.03% daily for 30 days
            decimal principal = 1000m;
            decimal dailyRate = 0.0003m;
            int days = 30;

            // Act
            var interest = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, dailyRate, days);

            // Assert
            Assert.True(interest > 8.9m && interest < 9.1m, $"Expected ~9 RZW interest, got {interest}");
        }

        [Fact]
        public void MonthlyPlan_OneMonth_ShouldCalculateCorrectly()
        {
            // Arrange - Monthly plan: 1% monthly for 30 days
            decimal principal = 5000m;
            decimal monthlyRate = 0.01m;
            var termType = RzwSavingsTermType.Monthly;
            int days = 30;

            // Convert monthly rate to daily rate
            var dailyRate = RzwSavingsCalculationHelper.CalculateDailyRate(monthlyRate, termType);

            // Act
            var result = RzwSavingsCalculationHelper.CalculateFinalAmount(principal, dailyRate, days);
            var interest = result - principal;

            // Assert - Monthly rate 0.01/30 = 0.000333333, so 5000 * (1.000333333)^30 - 5000 = ~50.24
            Assert.True(interest > 49m && interest < 52m, $"Expected ~50.24 RZW interest, got {interest}");
        }

        [Fact]
        public void YearlyPlan_OneYear_ShouldCalculateCorrectly()
        {
            // Arrange - Yearly plan: 15% yearly for 365 days
            decimal principal = 10000m;
            decimal yearlyRate = 0.15m;
            var termType = RzwSavingsTermType.Yearly;
            int days = 365;

            // Convert yearly rate to daily rate
            var dailyRate = RzwSavingsCalculationHelper.CalculateDailyRate(yearlyRate, termType);

            // Act
            var result = RzwSavingsCalculationHelper.CalculateFinalAmount(principal, dailyRate, days);
            var interest = result - principal;

            // Assert - Yearly rate 0.15/365 = 0.000410959, so 10000 * (1.000410959)^365 - 10000 = ~1617.98
            Assert.True(interest > 1610m && interest < 1625m, $"Expected ~1617.98 RZW interest, got {interest}");
        }

        #endregion
    }
}
