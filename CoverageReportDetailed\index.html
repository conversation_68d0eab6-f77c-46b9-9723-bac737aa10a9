<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="IE=EDGE,chrome=1" />
<link href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAn1BMVEUAAADCAAAAAAA3yDfUAAA3yDfUAAA8PDzr6+sAAAD4+Pg3yDeQkJDTAADt7e3V1dU3yDdCQkIAAADbMTHUAABBykHUAAA2yDY3yDfr6+vTAAB3diDR0dGYcHDUAAAjhiPSAAA3yDeuAADUAAA3yDf////OCALg9+BLzktBuzRelimzKgv87+/dNTVflSn1/PWz6rO126g5yDlYniy0KgwjJ0TyAAAAI3RSTlMABAj0WD6rJcsN7X1HzMqUJyYW+/X08+bltqSeaVRBOy0cE+citBEAAADBSURBVDjLlczXEoIwFIThJPYGiL0XiL3r+z+bBOJs9JDMuLffP8v+Gxfc6aIyDQVjQcnqnvRDEQwLJYtXpZT+YhDHKIjLbS+OUeT4TjkKi6OwOArq+yeKXD9uDqQQbcOjyCy0e6bTojZSftX+U6zUQ7OuittDu1k0WHqRFfdXQijgjKfF6ZwAikvmKD6OQjmKWUcDigkztm5FZN05nMON9ZcoinlBmTNnAUdBnRbUUbgdBZwWbkcBpwXcVsBtxfjb31j1QB5qeebOAAAAAElFTkSuQmCC" rel="icon" type="image/x-icon" />
<title>Summary - Coverage Report</title>
<link rel="stylesheet" type="text/css" href="report.css" />
</head><body><div class="container"><div class="containerleft">
<h1>Summary<a class="button" href="https://github.com/danielpalme/ReportGenerator" title="Star on GitHub"><i class="icon-star"></i>Star</a><a class="button" href="https://github.com/sponsors/danielpalme" title="Become a sponsor"><i class="icon-sponsor"></i>Sponsor</a></h1>
<div class="card-group">
<div class="card">
<div class="card-header">Information</div>
<div class="card-body">
<div class="table">
<table>
<tr>
<th>Parser:</th>
<td class="limit-width " title="Cobertura">Cobertura</td>
</tr>
<tr>
<th>Assemblies:</th>
<td class="limit-width right" title="1">1</td>
</tr>
<tr>
<th>Classes:</th>
<td class="limit-width right" title="55">55</td>
</tr>
<tr>
<th>Files:</th>
<td class="limit-width right" title="47">47</td>
</tr>
<tr>
<th>Coverage date:</th>
<td class="limit-width " title="6/29/2025 - 2:29:30 PM">6/29/2025 - 2:29:30 PM</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Line coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar91">8%</div>
<div class="table">
<table>
<tr>
<th>Covered lines:</th>
<td class="limit-width right" title="397">397</td>
</tr>
<tr>
<th>Uncovered lines:</th>
<td class="limit-width right" title="4159">4159</td>
</tr>
<tr>
<th>Coverable lines:</th>
<td class="limit-width right" title="4556">4556</td>
</tr>
<tr>
<th>Total lines:</th>
<td class="limit-width right" title="10394">10394</td>
</tr>
<tr>
<th>Line coverage:</th>
<td class="limit-width right" title="397 of 4556">8.7%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Branch coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar90">9%</div>
<div class="table">
<table>
<tr>
<th>Covered branches:</th>
<td class="limit-width right" title="81">81</td>
</tr>
<tr>
<th>Total branches:</th>
<td class="limit-width right" title="816">816</td>
</tr>
<tr>
<th>Branch coverage:</th>
<td class="limit-width right" title="81 of 816">9.9%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Method coverage</div>
<div class="card-body">
<div class="center">
<p>Feature is only available for sponsors</p>
<a class="pro-button" href="https://reportgenerator.io/pro" target="_blank">Upgrade to PRO version</a>
</div>
</div>
</div>
</div>
<h1>Risk Hotspots</h1>
<risk-hotspots>
<div class="table-responsive">
<table class="overview table-fixed stripped">
<colgroup>
<col class="column-min-200" />
<col class="column-min-200" />
<col class="column-min-200" />
<col class="column105" />
<col class="column105" />
</colgroup>
<thead><tr><th>Assembly</th>
<th>Class</th>
<th>Method</th>
<th>Crap Score <a href="https://googletesting.blogspot.de/2011/02/this-code-is-crap.html" target="_blank"><i class="icon-info-circled"></i></a></th>
<th>Cyclomatic complexity <a href="https://en.wikipedia.org/wiki/Cyclomatic_complexity" target="_blank"><i class="icon-info-circled"></i></a></th>
</tr></thead>
<tbody>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_Details.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.Areas_MyAccount_Pages_RzwSavings_Details</a></td>
<td title="ExecuteAsync()"><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_Details.html#file0_line7">ExecuteAsync()</a></td><td class="lightred right">3660</td>
<td class="lightred right">60</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_InterestHistory.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.Areas_MyAccount_Pages_RzwSavings_InterestHistory</a></td>
<td title="ExecuteAsync()"><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_InterestHistory.html#file0_line6">ExecuteAsync()</a></td><td class="lightred right">702</td>
<td class="lightred right">26</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_RzwSavingsService.html">RazeWinComTr.Areas.Admin.Services.RzwSavingsService</a></td>
<td title="ProcessMaturityWithInterestAndAutoRenewAsync()"><a href="RazeWinComTr_RzwSavingsService.html#file0_line322">ProcessMaturityWithInterestAndAutoRenewAsync()</a></td><td class="lightred right">600</td>
<td class="lightred right">24</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsAccounts_Details.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsAccounts.Areas_Admin_Pages_RzwSavingsAccounts_Details</a></td>
<td title="ExecuteAsync()"><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsAccounts_Details.html#file0_line7">ExecuteAsync()</a></td><td class="lightred right">506</td>
<td class="lightred right">22</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_CreateModel.2.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.CreateModel</a></td>
<td title="OnPostAsync()"><a href="RazeWinComTr_CreateModel.2.html#file0_line62">OnPostAsync()</a></td><td class="lightred right">506</td>
<td class="lightred right">22</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_Index.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.Areas_MyAccount_Pages_RzwSavings_Index</a></td>
<td title="ExecuteAsync()"><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_Index.html#file0_line7">ExecuteAsync()</a></td><td class="lightred right">420</td>
<td class="lightred right">20</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsAccounts_Index.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsAccounts.Areas_Admin_Pages_RzwSavingsAccounts_Index</a></td>
<td title="ExecuteAsync()"><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsAccounts_Index.html#file0_line7">ExecuteAsync()</a></td><td class="lightred right">272</td>
<td class="lightred right">16</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsPlans_Delete.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.Areas_Admin_Pages_RzwSavingsPlans_Delete</a></td>
<td title="ExecuteAsync()"><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsPlans_Delete.html#file0_line7">ExecuteAsync()</a></td><td class="lightred right">272</td>
<td class="lightred right">16</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_EditModel.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.EditModel</a></td>
<td title="OnPostAsync()"><a href="RazeWinComTr_EditModel.html#file0_line57">OnPostAsync()</a></td><td class="lightred right">272</td>
<td class="lightred right">16</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_Create.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.Areas_MyAccount_Pages_RzwSavings_Create</a></td>
<td title="&lt;ExecuteAsync()"><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_Create.html#file0_line81">&lt;ExecuteAsync()</a></td><td class="lightred right">272</td>
<td class="lightred right">16</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_History.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.Areas_MyAccount_Pages_RzwSavings_History</a></td>
<td title="ExecuteAsync()"><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_History.html#file0_line6">ExecuteAsync()</a></td><td class="lightred right">272</td>
<td class="lightred right">16</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_DetailsModel.2.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.DetailsModel</a></td>
<td title="CalculateSummary()"><a href="RazeWinComTr_DetailsModel.2.html#file0_line256">CalculateSummary()</a></td><td class="lightred right">272</td>
<td class="lightred right">16</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_CreateModel.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.CreateModel</a></td>
<td title="OnPostAsync()"><a href="RazeWinComTr_CreateModel.html#file0_line34">OnPostAsync()</a></td><td class="lightred right">210</td>
<td class="lightgreen right">14</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_RzwSavingsBackgroundService.html">RazeWinComTr.BackgroundServices.RzwSavingsBackgroundService</a></td>
<td title="ProcessAccountSafelyAsync()"><a href="RazeWinComTr_RzwSavingsBackgroundService.html#file0_line180">ProcessAccountSafelyAsync()</a></td><td class="lightred right">156</td>
<td class="lightgreen right">12</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_RzwSavingsController.html">RazeWinComTr.Areas.Admin.Controllers.RzwSavingsController</a></td>
<td title="DebugTriggerInterestForAccount()"><a href="RazeWinComTr_RzwSavingsController.html#file0_line176">DebugTriggerInterestForAccount()</a></td><td class="lightred right">110</td>
<td class="lightgreen right">10</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_RzwSavingsService.html">RazeWinComTr.Areas.Admin.Services.RzwSavingsService</a></td>
<td title="FixZeroInterestRateAccountsAsync()"><a href="RazeWinComTr_RzwSavingsService.html#file0_line578">FixZeroInterestRateAccountsAsync()</a></td><td class="lightred right">110</td>
<td class="lightgreen right">10</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_RzwSavingsService.html">RazeWinComTr.Areas.Admin.Services.RzwSavingsService</a></td>
<td title="GetAccountsForAdminAsync()"><a href="RazeWinComTr_RzwSavingsService.html#file0_line474">GetAccountsForAdminAsync()</a></td><td class="lightred right">110</td>
<td class="lightgreen right">10</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_Create.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.Areas_MyAccount_Pages_RzwSavings_Create</a></td>
<td title="ExecuteAsync()"><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_Create.html#file0_line7">ExecuteAsync()</a></td><td class="lightred right">110</td>
<td class="lightgreen right">10</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_DetailsModel.2.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.DetailsModel</a></td>
<td title="OnPostClaimInterestAsync()"><a href="RazeWinComTr_DetailsModel.2.html#file0_line143">OnPostClaimInterestAsync()</a></td><td class="lightred right">110</td>
<td class="lightgreen right">10</td>
</tr>
<tr>
<td>RazeWinComTr</td>
<td><a href="RazeWinComTr_InterestHistoryModel.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.InterestHistoryModel</a></td>
<td title="ExportToCsvAsync(System.Collections.Generic.List`1&lt;RazeWinComTr.Areas.Admin.DbModel.RzwSavingsInterestPayment&gt;)"><a href="RazeWinComTr_InterestHistoryModel.html#file0_line181">ExportToCsvAsync(...)</a></td><td class="lightred right">110</td>
<td class="lightgreen right">10</td>
</tr>
</tbody>
</table>
</div>
</risk-hotspots>
<h1>Coverage</h1>
<coverage-info>
<div class="table-responsive">
<table class="overview table-fixed stripped">
<colgroup>
<col class="column-min-200" />
<col class="column90" />
<col class="column105" />
<col class="column100" />
<col class="column70" />
<col class="column60" />
<col class="column112" />
<col class="column90" />
<col class="column70" />
<col class="column60" />
<col class="column112" />
</colgroup>
<thead>
<tr class="header"><th></th><th colspan="6" class="center">Line coverage</th><th colspan="4" class="center">Branch coverage</th></tr>
<tr><th>Name</th><th class="right">Covered</th><th class="right">Uncovered</th><th class="right">Coverable</th><th class="right">Total</th><th class="center" colspan="2">Percentage</th><th class="right">Covered</th><th class="right">Total</th><th class="center" colspan="2">Percentage</th></tr></thead>
<tbody>
<tr><th>RazeWinComTr</th><th class="right">397</th><th class="right">4159</th><th class="right">4556</th><th class="right">12411</th><th title="397/4556" class="right">8.7%</th><th><table class="coverage"><tr><td class="green covered9">&nbsp;</td><td class="red covered91">&nbsp;</td></tr></table></th><th class="right">81</th><th class="right">816</th><th class="right" title="81/816">9.9%</th><th><table class="coverage"><tr><td class="green covered10">&nbsp;</td><td class="red covered90">&nbsp;</td></tr></table></th></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsController.html">RazeWinComTr.Areas.Admin.Controllers.RzwSavingsController</a></td><td class="right">0</td><td class="right">227</td><td class="right">227</td><td class="right">335</td><td title="0/227" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">28</td><td class="right" title="0/28">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsAccount.html">RazeWinComTr.Areas.Admin.DbModel.RzwSavingsAccount</a></td><td class="right">20</td><td class="right">11</td><td class="right">31</td><td class="right">120</td><td title="20/31" class="right">64.5%</td><td><table class="coverage"><tr><td class="green covered64">&nbsp;</td><td class="red covered36">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">4</td><td class="right" title="0/4">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsConstants.html">RazeWinComTr.Areas.Admin.DbModel.RzwSavingsConstants</a></td><td class="right">0</td><td class="right">3</td><td class="right">3</td><td class="right">59</td><td title="0/3" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsInterestPayment.html">RazeWinComTr.Areas.Admin.DbModel.RzwSavingsInterestPayment</a></td><td class="right">6</td><td class="right">6</td><td class="right">12</td><td class="right">52</td><td title="6/12" class="right">50%</td><td><table class="coverage"><tr><td class="green covered50">&nbsp;</td><td class="red covered50">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsPlan.html">RazeWinComTr.Areas.Admin.DbModel.RzwSavingsPlan</a></td><td class="right">11</td><td class="right">2</td><td class="right">13</td><td class="right">59</td><td title="11/13" class="right">84.6%</td><td><table class="coverage"><tr><td class="green covered85">&nbsp;</td><td class="red covered15">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsCalculationHelper.html">RazeWinComTr.Areas.Admin.Helpers.RzwSavingsCalculationHelper</a></td><td class="right">28</td><td class="right">114</td><td class="right">142</td><td class="right">450</td><td title="28/142" class="right">19.7%</td><td><table class="coverage"><tr><td class="green covered20">&nbsp;</td><td class="red covered80">&nbsp;</td></tr></table></td><td class="right">17</td><td class="right">68</td><td class="right" title="17/68">25%</td><td><table class="coverage"><tr><td class="green covered25">&nbsp;</td><td class="red covered75">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsDisplayHelper.html">RazeWinComTr.Areas.Admin.Helpers.RzwSavingsDisplayHelper</a></td><td class="right">0</td><td class="right">30</td><td class="right">30</td><td class="right">111</td><td title="0/30" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">12</td><td class="right" title="0/12">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsViewModelExtensions.html">RazeWinComTr.Areas.Admin.Helpers.RzwSavingsViewModelExtensions</a></td><td class="right">0</td><td class="right">6</td><td class="right">6</td><td class="right">111</td><td title="0/6" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavings_DebugTest.html">RazeWinComTr.Areas.Admin.Pages.RzwSavings.Areas_Admin_Pages_RzwSavings_DebugTest</a></td><td class="right">0</td><td class="right">2</td><td class="right">2</td><td class="right">384</td><td title="0/2" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_DebugTestModel.html">RazeWinComTr.Areas.Admin.Pages.RzwSavings.DebugTestModel</a></td><td class="right">0</td><td class="right">11</td><td class="right">11</td><td class="right">28</td><td title="0/11" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsAccounts_Details.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsAccounts.Areas_Admin_Pages_RzwSavingsAccounts_Details</a></td><td class="right">0</td><td class="right">16</td><td class="right">16</td><td class="right">183</td><td title="0/16" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">20</td><td class="right" title="0/20">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsAccounts_Index.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsAccounts.Areas_Admin_Pages_RzwSavingsAccounts_Index</a></td><td class="right">0</td><td class="right">20</td><td class="right">20</td><td class="right">185</td><td title="0/20" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">16</td><td class="right" title="0/16">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_DetailsModel.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsAccounts.DetailsModel</a></td><td class="right">0</td><td class="right">58</td><td class="right">58</td><td class="right">92</td><td title="0/58" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">8</td><td class="right" title="0/8">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_IndexModel.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsAccounts.IndexModel</a></td><td class="right">0</td><td class="right">28</td><td class="right">28</td><td class="right">59</td><td title="0/28" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsMonitoring_Index.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsMonitoring.Areas_Admin_Pages_RzwSavingsMonitoring_Index</a></td><td class="right">0</td><td class="right">5</td><td class="right">5</td><td class="right">182</td><td title="0/5" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">6</td><td class="right" title="0/6">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_IndexModel.2.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsMonitoring.IndexModel</a></td><td class="right">0</td><td class="right">12</td><td class="right">12</td><td class="right">25</td><td title="0/12" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsPlans_Create.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.Areas_Admin_Pages_RzwSavingsPlans_Create</a></td><td class="right">0</td><td class="right">5</td><td class="right">5</td><td class="right">146</td><td title="0/5" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">6</td><td class="right" title="0/6">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsPlans_Delete.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.Areas_Admin_Pages_RzwSavingsPlans_Delete</a></td><td class="right">0</td><td class="right">16</td><td class="right">16</td><td class="right">119</td><td title="0/16" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">16</td><td class="right" title="0/16">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsPlans_Edit.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.Areas_Admin_Pages_RzwSavingsPlans_Edit</a></td><td class="right">0</td><td class="right">5</td><td class="right">5</td><td class="right">147</td><td title="0/5" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">6</td><td class="right" title="0/6">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsPlans_Index.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.Areas_Admin_Pages_RzwSavingsPlans_Index</a></td><td class="right">0</td><td class="right">5</td><td class="right">5</td><td class="right">89</td><td title="0/5" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">6</td><td class="right" title="0/6">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_CreateModel.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.CreateModel</a></td><td class="right">0</td><td class="right">79</td><td class="right">79</td><td class="right">139</td><td title="0/79" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">14</td><td class="right" title="0/14">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_DeleteModel.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.DeleteModel</a></td><td class="right">0</td><td class="right">62</td><td class="right">62</td><td class="right">103</td><td title="0/62" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">10</td><td class="right" title="0/10">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_EditModel.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.EditModel</a></td><td class="right">0</td><td class="right">101</td><td class="right">101</td><td class="right">167</td><td title="0/101" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">20</td><td class="right" title="0/20">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_IndexModel.3.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.IndexModel</a></td><td class="right">0</td><td class="right">18</td><td class="right">18</td><td class="right">36</td><td title="0/18" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">2</td><td class="right" title="0/2">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_Admin_Pages_RzwSavingsReports_Index.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsReports.Areas_Admin_Pages_RzwSavingsReports_Index</a></td><td class="right">0</td><td class="right">9</td><td class="right">9</td><td class="right">219</td><td title="0/9" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">6</td><td class="right" title="0/6">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_IndexModel.4.html">RazeWinComTr.Areas.Admin.Pages.RzwSavingsReports.IndexModel</a></td><td class="right">0</td><td class="right">18</td><td class="right">18</td><td class="right">39</td><td title="0/18" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">4</td><td class="right" title="0/4">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwBalanceManagementService.html">RazeWinComTr.Areas.Admin.Services.RzwBalanceManagementService</a></td><td class="right">116</td><td class="right">8</td><td class="right">124</td><td class="right">236</td><td title="116/124" class="right">93.5%</td><td><table class="coverage"><tr><td class="green covered94">&nbsp;</td><td class="red covered6">&nbsp;</td></tr></table></td><td class="right">9</td><td class="right">12</td><td class="right" title="9/12">75%</td><td><table class="coverage"><tr><td class="green covered75">&nbsp;</td><td class="red covered25">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsInterestService.html">RazeWinComTr.Areas.Admin.Services.RzwSavingsInterestService</a></td><td class="right">93</td><td class="right">114</td><td class="right">207</td><td class="right">409</td><td title="93/207" class="right">44.9%</td><td><table class="coverage"><tr><td class="green covered45">&nbsp;</td><td class="red covered55">&nbsp;</td></tr></table></td><td class="right">21</td><td class="right">44</td><td class="right" title="21/44">47.7%</td><td><table class="coverage"><tr><td class="green covered48">&nbsp;</td><td class="red covered52">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsMonitoringService.html">RazeWinComTr.Areas.Admin.Services.RzwSavingsMonitoringService</a></td><td class="right">0</td><td class="right">84</td><td class="right">84</td><td class="right">163</td><td title="0/84" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsPlanService.html">RazeWinComTr.Areas.Admin.Services.RzwSavingsPlanService</a></td><td class="right">0</td><td class="right">136</td><td class="right">136</td><td class="right">252</td><td title="0/136" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">16</td><td class="right" title="0/16">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsService.html">RazeWinComTr.Areas.Admin.Services.RzwSavingsService</a></td><td class="right">119</td><td class="right">283</td><td class="right">402</td><td class="right">679</td><td title="119/402" class="right">29.6%</td><td><table class="coverage"><tr><td class="green covered30">&nbsp;</td><td class="red covered70">&nbsp;</td></tr></table></td><td class="right">34</td><td class="right">102</td><td class="right" title="34/102">33.3%</td><td><table class="coverage"><tr><td class="green covered33">&nbsp;</td><td class="red covered67">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsAccountViewModel.html">RazeWinComTr.Areas.Admin.ViewModels.RzwSavings.RzwSavingsAccountViewModel</a></td><td class="right">0</td><td class="right">75</td><td class="right">75</td><td class="right">98</td><td title="0/75" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">26</td><td class="right" title="0/26">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsPlanViewModel.html">RazeWinComTr.Areas.Admin.ViewModels.RzwSavings.RzwSavingsPlanViewModel</a></td><td class="right">0</td><td class="right">59</td><td class="right">59</td><td class="right">78</td><td title="0/59" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">12</td><td class="right" title="0/12">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_Create.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.Areas_MyAccount_Pages_RzwSavings_Create</a></td><td class="right">0</td><td class="right">13</td><td class="right">13</td><td class="right">211</td><td title="0/13" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">26</td><td class="right" title="0/26">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_Details.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.Areas_MyAccount_Pages_RzwSavings_Details</a></td><td class="right">0</td><td class="right">61</td><td class="right">61</td><td class="right">596</td><td title="0/61" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">62</td><td class="right" title="0/62">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_History.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.Areas_MyAccount_Pages_RzwSavings_History</a></td><td class="right">0</td><td class="right">21</td><td class="right">21</td><td class="right">241</td><td title="0/21" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">16</td><td class="right" title="0/16">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_Index.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.Areas_MyAccount_Pages_RzwSavings_Index</a></td><td class="right">0</td><td class="right">29</td><td class="right">29</td><td class="right">407</td><td title="0/29" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">20</td><td class="right" title="0/20">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_Areas_MyAccount_Pages_RzwSavings_InterestHistory.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.Areas_MyAccount_Pages_RzwSavings_InterestHistory</a></td><td class="right">0</td><td class="right">24</td><td class="right">24</td><td class="right">349</td><td title="0/24" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">32</td><td class="right" title="0/32">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_CreateModel.2.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.CreateModel</a></td><td class="right">0</td><td class="right">161</td><td class="right">161</td><td class="right">276</td><td title="0/161" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">36</td><td class="right" title="0/36">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_CreateSavingsAccountViewModel.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.CreateSavingsAccountViewModel</a></td><td class="right">0</td><td class="right">3</td><td class="right">3</td><td class="right">276</td><td title="0/3" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_DetailsModel.2.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.DetailsModel</a></td><td class="right">0</td><td class="right">227</td><td class="right">227</td><td class="right">336</td><td title="0/227" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">44</td><td class="right" title="0/44">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_HistoryModel.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.HistoryModel</a></td><td class="right">0</td><td class="right">92</td><td class="right">92</td><td class="right">200</td><td title="0/92" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">16</td><td class="right" title="0/16">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_IndexModel.5.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.IndexModel</a></td><td class="right">0</td><td class="right">96</td><td class="right">96</td><td class="right">212</td><td title="0/96" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">24</td><td class="right" title="0/24">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_InterestHistoryModel.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.InterestHistoryModel</a></td><td class="right">0</td><td class="right">125</td><td class="right">125</td><td class="right">235</td><td title="0/125" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">32</td><td class="right" title="0/32">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_InterestHistorySummary.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.InterestHistorySummary</a></td><td class="right">0</td><td class="right">7</td><td class="right">7</td><td class="right">235</td><td title="0/7" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_MonthlyInterestSummary.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.MonthlyInterestSummary</a></td><td class="right">0</td><td class="right">5</td><td class="right">5</td><td class="right">235</td><td title="0/5" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsAccountDisplayModel.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.RzwSavingsAccountDisplayModel</a></td><td class="right">0</td><td class="right">23</td><td class="right">23</td><td class="right">212</td><td title="0/23" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">2</td><td class="right" title="0/2">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsAccountHistoryDisplayModel.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.RzwSavingsAccountHistoryDisplayModel</a></td><td class="right">0</td><td class="right">29</td><td class="right">29</td><td class="right">200</td><td title="0/29" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">10</td><td class="right" title="0/10">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsDashboard.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.RzwSavingsDashboard</a></td><td class="right">0</td><td class="right">6</td><td class="right">6</td><td class="right">212</td><td title="0/6" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsHistorySummary.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.RzwSavingsHistorySummary</a></td><td class="right">0</td><td class="right">8</td><td class="right">8</td><td class="right">200</td><td title="0/8" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_SavingsAccountSummary.html">RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.SavingsAccountSummary</a></td><td class="right">0</td><td class="right">12</td><td class="right">12</td><td class="right">336</td><td title="0/12" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">4</td><td class="right" title="0/4">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsBackgroundService.html">RazeWinComTr.BackgroundServices.RzwSavingsBackgroundService</a></td><td class="right">0</td><td class="right">114</td><td class="right">114</td><td class="right">195</td><td title="0/114" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">28</td><td class="right" title="0/28">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_AddRzwSavingsSupport.html">RazeWinComTr.Migrations.AddRzwSavingsSupport</a></td><td class="right">0</td><td class="right">1457</td><td class="right">1457</td><td class="right">1548</td><td title="0/1457" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwBalanceInfo.html">RazeWinComTr.Models.RzwBalanceInfo</a></td><td class="right">4</td><td class="right">5</td><td class="right">9</td><td class="right">54</td><td title="4/9" class="right">44.4%</td><td><table class="coverage"><tr><td class="green covered44">&nbsp;</td><td class="red covered56">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="RazeWinComTr_RzwSavingsPlanDisplayModel.html">RazeWinComTr.Pages.RzwSavingsPlanDisplayModel</a></td><td class="right">0</td><td class="right">13</td><td class="right">13</td><td class="right">91</td><td title="0/13" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
</tbody>
</table>
</div>
</coverage-info>
<div class="footer">Generated by: ReportGenerator *******<br />6/29/2025 - 2:31:29 PM<br /><a href="https://github.com/danielpalme/ReportGenerator">GitHub</a> | <a href="https://reportgenerator.io">reportgenerator.io</a></div></div></div>
<script type="text/javascript">
/* <![CDATA[ */
(function() {
    var url = window.location.href;
    var startOfQueryString = url.indexOf('?');
    var queryString = startOfQueryString > -1 ? url.substr(startOfQueryString) : '';

    if (startOfQueryString > -1) {
        var i = 0, href= null;
        var css = document.getElementsByTagName('link');

        for (i = 0; i < css.length; i++) {
            if (css[i].getAttribute('rel') !== 'stylesheet') {
            continue;
            }

            href = css[i].getAttribute('href');

            if (href) {
            css[i].setAttribute('href', href + queryString);
            }
        }

        var links = document.getElementsByTagName('a');

        for (i = 0; i < links.length; i++) {
            href = links[i].getAttribute('href');

            if (href
                && !href.startsWith('http://')
                && !href.startsWith('https://')
                && !href.startsWith('#')
                && href.indexOf('?') === -1) {
            links[i].setAttribute('href', href + queryString);
            }
        }
    }

    var newScript = document.createElement('script');
    newScript.src = 'main.js' + queryString;
    document.getElementsByTagName('body')[0].appendChild(newScript);
})();
/* ]]> */ 
</script></body></html>