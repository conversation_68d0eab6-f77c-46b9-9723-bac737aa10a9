using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Models;
using Xunit;

namespace RazeWinComTr.RewardTests.Unit.Services
{
    public class RzwBalanceManagementServiceTests : IDisposable
    {
        private readonly AppDbContext _context;
        private readonly Mock<IWalletService> _mockWalletService;
        private readonly Mock<ITokenPriceService> _mockTokenPriceService;
        private readonly Mock<ITradeService> _mockTradeService;
        private readonly RzwBalanceManagementService _service;

        public RzwBalanceManagementServiceTests()
        {
            // Setup in-memory database
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .ConfigureWarnings(warnings => warnings.Ignore(InMemoryEventId.TransactionIgnoredWarning))
                .Options;
            _context = new AppDbContext(options);

            // Setup mocks
            _mockWalletService = new Mock<IWalletService>();
            _mockTokenPriceService = new Mock<ITokenPriceService>();
            _mockTradeService = new Mock<ITradeService>();

            // Create service instance
            _service = new RzwBalanceManagementService(
                _context,
                _mockWalletService.Object,
                _mockTokenPriceService.Object,
                _mockTradeService.Object
            );
        }

        public void Dispose()
        {
            _context.Dispose();
        }

        #region Lock RZW for Savings Tests

        [Fact]
        public async Task LockRzwForSavingsAsync_WithSufficientBalance_ShouldLockBalanceAndCreateTrade()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            var wallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 10000m,
                LockedBalance = 0m
            };

            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.Wallets.AddAsync(wallet);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.LockRzwForSavingsAsync(1, 5000m, "Test savings lock", _context, 1);

            // Assert
            Assert.True(result);

            var updatedWallet = await _context.Wallets.FirstAsync(w => w.UserId == 1 && w.CoinId == 1);
            Assert.Equal(5000m, updatedWallet.Balance); // 10000 - 5000
            Assert.Equal(5000m, updatedWallet.LockedBalance); // 0 + 5000

            var trade = await _context.Trades
                .FirstOrDefaultAsync(t => t.UserId == 1 && t.CoinAmount == 5000m && t.Type == TradeType.RzwSavingsDeposit);
            Assert.NotNull(trade);
            Assert.Equal(1, trade.CoinId);
        }

        [Fact]
        public async Task LockRzwForSavingsAsync_WithInsufficientBalance_ShouldReturnFalse()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            var wallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 3000m, // Less than required
                LockedBalance = 0m
            };

            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.Wallets.AddAsync(wallet);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.LockRzwForSavingsAsync(1, 5000m, "Test savings lock", _context, 1);

            // Assert
            Assert.False(result);

            var wallet_after = await _context.Wallets.FirstAsync(w => w.UserId == 1 && w.CoinId == 1);
            Assert.Equal(3000m, wallet_after.Balance); // Unchanged
            Assert.Equal(0m, wallet_after.LockedBalance); // Unchanged

            var trade = await _context.Trades
                .FirstOrDefaultAsync(t => t.UserId == 1 && t.Type == TradeType.RzwSavingsDeposit);
            Assert.Null(trade); // No trade should be created
        }

        [Fact]
        public async Task LockRzwForSavingsAsync_WithNoRzwWallet_ShouldReturnFalse()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.LockRzwForSavingsAsync(1, 5000m, "Test savings lock", _context, 1);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task LockRzwForSavingsAsync_WithZeroAmount_ShouldReturnFalse()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            var wallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 10000m,
                LockedBalance = 0m
            };

            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.Wallets.AddAsync(wallet);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.LockRzwForSavingsAsync(1, 0m, "Test savings lock", _context, 1);

            // Assert
            Assert.False(result);
        }

        #endregion

        #region Unlock RZW from Savings Tests

        [Fact]
        public async Task UnlockRzwFromSavingsAsync_WithSufficientLockedBalance_ShouldUnlockBalanceAndCreateTrade()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            var wallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 5000m,
                LockedBalance = 5000m
            };

            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.Wallets.AddAsync(wallet);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.UnlockRzwFromSavingsAsync(1, 5000m, "Test savings unlock", TradeType.RzwSavingsWithdrawal, _context, 1);

            // Assert
            Assert.True(result);

            var updatedWallet = await _context.Wallets.FirstAsync(w => w.UserId == 1 && w.CoinId == 1);
            Assert.Equal(10000m, updatedWallet.Balance); // 5000 + 5000
            Assert.Equal(0m, updatedWallet.LockedBalance); // 5000 - 5000

            var trade = await _context.Trades
                .FirstOrDefaultAsync(t => t.UserId == 1 && t.CoinAmount == 5000m && t.Type == TradeType.RzwSavingsWithdrawal);
            Assert.NotNull(trade);
            Assert.Equal(1, trade.CoinId);
        }

        [Fact]
        public async Task UnlockRzwFromSavingsAsync_WithInsufficientLockedBalance_ShouldReturnFalse()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            var wallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 5000m,
                LockedBalance = 3000m // Less than required
            };

            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.Wallets.AddAsync(wallet);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.UnlockRzwFromSavingsAsync(1, 5000m, "Test savings unlock", TradeType.RzwSavingsWithdrawal, _context, 1);

            // Assert
            Assert.False(result);

            var wallet_after = await _context.Wallets.FirstAsync(w => w.UserId == 1 && w.CoinId == 1);
            Assert.Equal(5000m, wallet_after.Balance); // Unchanged
            Assert.Equal(3000m, wallet_after.LockedBalance); // Unchanged
        }

        #endregion

        #region Add RZW Interest Tests

        [Fact]
        public async Task AddRzwInterestAsync_WithValidAmount_ShouldAddToAvailableBalanceAndCreateTrade()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            var wallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 5000m,
                LockedBalance = 0m
            };

            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.Wallets.AddAsync(wallet);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.AddRzwInterestAsync(1, 25.5m, "Test interest payment", _context, 1);

            // Assert
            Assert.NotNull(result);

            var updatedWallet = await _context.Wallets.FirstAsync(w => w.UserId == 1 && w.CoinId == 1);
            Assert.Equal(5025.5m, updatedWallet.Balance); // 5000 + 25.5
            Assert.Equal(0m, updatedWallet.LockedBalance); // Unchanged

            var trade = await _context.Trades
                .FirstOrDefaultAsync(t => t.UserId == 1 && t.CoinAmount == 25.5m && t.Type == TradeType.RzwSavingsInterest);
            Assert.NotNull(trade);
            Assert.Equal(1, trade.CoinId);
        }

        [Fact]
        public async Task AddRzwInterestAsync_WithNoRzwWallet_ShouldCreateWalletAndAddInterest()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.AddRzwInterestAsync(1, 25.5m, "Test interest payment", _context, 1);

            // Assert
            Assert.NotNull(result);

            var wallet = await _context.Wallets.FirstOrDefaultAsync(w => w.UserId == 1 && w.CoinId == 1);
            Assert.NotNull(wallet);
            Assert.Equal(25.5m, wallet.Balance);
            Assert.Equal(0m, wallet.LockedBalance);

            var trade = await _context.Trades
                .FirstOrDefaultAsync(t => t.UserId == 1 && t.CoinAmount == 25.5m && t.Type == TradeType.RzwSavingsInterest);
            Assert.NotNull(trade);
        }

        [Fact]
        public async Task AddRzwInterestAsync_WithZeroAmount_ShouldThrowException()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.SaveChangesAsync();

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() =>
                _service.AddRzwInterestAsync(1, 0m, "Test interest payment", _context, 1));
        }

        [Fact]
        public async Task AddRzwInterestAsync_WithNegativeAmount_ShouldThrowException()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.SaveChangesAsync();

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() =>
                _service.AddRzwInterestAsync(1, -10m, "Test interest payment", _context, 1));
        }

        #endregion

        #region Get RZW Balance Tests

        [Fact]
        public async Task GetRzwBalanceInfoAsync_WithExistingWallet_ShouldReturnCorrectBalance()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            var wallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 7500.25m,
                LockedBalance = 2500m
            };

            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.Wallets.AddAsync(wallet);
            await _context.SaveChangesAsync();

            // Setup mock
            _mockWalletService.Setup(x => x.GetWalletBalanceInfoAsync(1, 1))
                .ReturnsAsync(new WalletBalanceInfo
                {
                    AvailableBalance = 7500.25m,
                    LockedBalance = 2500m,
                    ActiveSavingsAccountCount = 0
                });

            // Act
            var result = await _service.GetRzwBalanceInfoAsync(1);

            // Assert
            Assert.Equal(7500.25m, result.AvailableRzw);
            Assert.Equal(2500m, result.LockedRzw);
        }

        [Fact]
        public async Task GetRzwBalanceInfoAsync_WithNoWallet_ShouldReturnZeroBalance()
        {
            // Setup mock
            _mockWalletService.Setup(x => x.GetWalletBalanceInfoAsync(999, 1))
                .ReturnsAsync(new WalletBalanceInfo
                {
                    AvailableBalance = 0m,
                    LockedBalance = 0m,
                    ActiveSavingsAccountCount = 0
                });

            // Act
            var result = await _service.GetRzwBalanceInfoAsync(999);

            // Assert
            Assert.Equal(0m, result.AvailableRzw);
            Assert.Equal(0m, result.LockedRzw);
        }

        [Fact]
        public async Task HasSufficientAvailableRzwAsync_WithSufficientBalance_ShouldReturnTrue()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            var wallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 7500.25m,
                LockedBalance = 2500.75m
            };

            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.Wallets.AddAsync(wallet);
            await _context.SaveChangesAsync();

            // Setup mock
            _mockWalletService.Setup(x => x.GetWalletBalanceInfoAsync(1, 1))
                .ReturnsAsync(new WalletBalanceInfo
                {
                    AvailableBalance = 7500.25m,
                    LockedBalance = 2500.75m,
                    ActiveSavingsAccountCount = 0
                });

            // Act
            var result = await _service.HasSufficientAvailableRzwAsync(1, 5000m);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task HasSufficientAvailableRzwAsync_WithInsufficientBalance_ShouldReturnFalse()
        {
            // Setup mock
            _mockWalletService.Setup(x => x.GetWalletBalanceInfoAsync(999, 1))
                .ReturnsAsync(new WalletBalanceInfo
                {
                    AvailableBalance = 0m,
                    LockedBalance = 0m,
                    ActiveSavingsAccountCount = 0
                });

            // Act
            var result = await _service.HasSufficientAvailableRzwAsync(999, 5000m);

            // Assert
            Assert.False(result);
        }

        #endregion
    }
}
