<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="IE=EDGE,chrome=1" />
<link href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAn1BMVEUAAADCAAAAAAA3yDfUAAA3yDfUAAA8PDzr6+sAAAD4+Pg3yDeQkJDTAADt7e3V1dU3yDdCQkIAAADbMTHUAABBykHUAAA2yDY3yDfr6+vTAAB3diDR0dGYcHDUAAAjhiPSAAA3yDeuAADUAAA3yDf////OCALg9+BLzktBuzRelimzKgv87+/dNTVflSn1/PWz6rO126g5yDlYniy0KgwjJ0TyAAAAI3RSTlMABAj0WD6rJcsN7X1HzMqUJyYW+/X08+bltqSeaVRBOy0cE+citBEAAADBSURBVDjLlczXEoIwFIThJPYGiL0XiL3r+z+bBOJs9JDMuLffP8v+Gxfc6aIyDQVjQcnqnvRDEQwLJYtXpZT+YhDHKIjLbS+OUeT4TjkKi6OwOArq+yeKXD9uDqQQbcOjyCy0e6bTojZSftX+U6zUQ7OuittDu1k0WHqRFfdXQijgjKfF6ZwAikvmKD6OQjmKWUcDigkztm5FZN05nMON9ZcoinlBmTNnAUdBnRbUUbgdBZwWbkcBpwXcVsBtxfjb31j1QB5qeebOAAAAAElFTkSuQmCC" rel="icon" type="image/x-icon" />
<title>RazeWinComTr.Areas.Admin.ViewModels.RzwSavings.RzwSavingsPlanViewModel - Coverage Report</title>
<link rel="stylesheet" type="text/css" href="report.css" />
</head><body><div class="container"><div class="containerleft">
<h1><a href="index.html" class="back">&lt;</a> Summary</h1>
<div class="card-group">
<div class="card">
<div class="card-header">Information</div>
<div class="card-body">
<div class="table">
<table>
<tr>
<th>Class:</th>
<td class="limit-width " title="RazeWinComTr.Areas.Admin.ViewModels.RzwSavings.RzwSavingsPlanViewModel">RazeWinComTr.Areas.Admin.ViewModels.RzwSavings.RzwSavingsPlanViewModel</td>
</tr>
<tr>
<th>Assembly:</th>
<td class="limit-width " title="RazeWinComTr">RazeWinComTr</td>
</tr>
<tr>
<th>File(s):</th>
<td class="overflow-wrap"><a href="#CtempRZWRZWWEBsrcAreasAdminViewModelsRzwSavingsRzwSavingsPlanViewModelcs" class="navigatetohash">C:\temp\RZW\RZWWEB\src\Areas\Admin\ViewModels\RzwSavings\RzwSavingsPlanViewModel.cs</a></td>
</tr>
</table>
</div>
</div>
</div>
</div>
<div class="card-group">
<div class="card">
<div class="card-header">Line coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar100">0%</div>
<div class="table">
<table>
<tr>
<th>Covered lines:</th>
<td class="limit-width right" title="0">0</td>
</tr>
<tr>
<th>Uncovered lines:</th>
<td class="limit-width right" title="59">59</td>
</tr>
<tr>
<th>Coverable lines:</th>
<td class="limit-width right" title="59">59</td>
</tr>
<tr>
<th>Total lines:</th>
<td class="limit-width right" title="78">78</td>
</tr>
<tr>
<th>Line coverage:</th>
<td class="limit-width right" title="0 of 59">0%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Branch coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar100">0%</div>
<div class="table">
<table>
<tr>
<th>Covered branches:</th>
<td class="limit-width right" title="0">0</td>
</tr>
<tr>
<th>Total branches:</th>
<td class="limit-width right" title="12">12</td>
</tr>
<tr>
<th>Branch coverage:</th>
<td class="limit-width right" title="0 of 12">0%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Method coverage</div>
<div class="card-body">
<div class="center">
<p>Feature is only available for sponsors</p>
<a class="pro-button" href="https://reportgenerator.io/pro" target="_blank">Upgrade to PRO version</a>
</div>
</div>
</div>
</div>
<h1>Metrics</h1>
<div class="table-responsive">
<table class="overview table-fixed">
<colgroup>
<col class="column-min-200" />
<col class="column105" />
<col class="column105" />
<col class="column105" />
<col class="column105" />
</colgroup>
<thead><tr><th>Method</th><th>Branch coverage <a href="https://en.wikipedia.org/wiki/Code_coverage" target="_blank"><i class="icon-info-circled"></i></a></th><th>Crap Score <a href="https://googletesting.blogspot.de/2011/02/this-code-is-crap.html" target="_blank"><i class="icon-info-circled"></i></a></th><th>Cyclomatic complexity <a href="https://en.wikipedia.org/wiki/Cyclomatic_complexity" target="_blank"><i class="icon-info-circled"></i></a></th><th>Line coverage <a href="https://en.wikipedia.org/wiki/Code_coverage" target="_blank"><i class="icon-info-circled"></i></a></th></tr></thead>
<tbody>
<tr><td title="get_Id()"><a href="#file0_line7" class="navigatetohash">get_Id()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_Name()"><a href="#file0_line8" class="navigatetohash">get_Name()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_TermType()"><a href="#file0_line9" class="navigatetohash">get_TermType()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_TermDuration()"><a href="#file0_line10" class="navigatetohash">get_TermDuration()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_InterestRate()"><a href="#file0_line11" class="navigatetohash">get_InterestRate()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_MinRzwAmount()"><a href="#file0_line12" class="navigatetohash">get_MinRzwAmount()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_MaxRzwAmount()"><a href="#file0_line13" class="navigatetohash">get_MaxRzwAmount()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_IsActive()"><a href="#file0_line14" class="navigatetohash">get_IsActive()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_DisplayOrder()"><a href="#file0_line15" class="navigatetohash">get_DisplayOrder()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_Description()"><a href="#file0_line16" class="navigatetohash">get_Description()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_CreatedDate()"><a href="#file0_line17" class="navigatetohash">get_CreatedDate()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_ModifiedDate()"><a href="#file0_line18" class="navigatetohash">get_ModifiedDate()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_TotalAccounts()"><a href="#file0_line21" class="navigatetohash">get_TotalAccounts()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_TotalLockedRzw()"><a href="#file0_line22" class="navigatetohash">get_TotalLockedRzw()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_TotalInterestPaid()"><a href="#file0_line23" class="navigatetohash">get_TotalInterestPaid()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_TermDisplayText()"><a href="#file0_line26" class="navigatetohash">get_TermDisplayText()</a></td><td>0%</td><td>42</td><td>6</td><td>0%</td></tr>
<tr><td title="get_InterestRateDisplayText()"><a href="#file0_line34" class="navigatetohash">get_InterestRateDisplayText()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="get_StatusDisplayText()"><a href="#file0_line36" class="navigatetohash">get_StatusDisplayText()</a></td><td>0%</td><td>6</td><td>2</td><td>0%</td></tr>
<tr><td title="get_MaxAmountDisplayText()"><a href="#file0_line38" class="navigatetohash">get_MaxAmountDisplayText()</a></td><td>0%</td><td>20</td><td>4</td><td>0%</td></tr>
<tr><td title="FromEntity(RazeWinComTr.Areas.Admin.DbModel.RzwSavingsPlan)"><a href="#file0_line41" class="navigatetohash">FromEntity(...)</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
<tr><td title="ToEntity()"><a href="#file0_line60" class="navigatetohash">ToEntity()</a></td><td>100%</td><td>2</td><td>1</td><td>0%</td></tr>
</tbody>
</table>
</div>
<h1>File(s)</h1>
<h2 id="CtempRZWRZWWEBsrcAreasAdminViewModelsRzwSavingsRzwSavingsPlanViewModelcs">C:\temp\RZW\RZWWEB\src\Areas\Admin\ViewModels\RzwSavings\RzwSavingsPlanViewModel.cs</h2>
<div class="table-responsive">
<table class="lineAnalysis">
<thead><tr><th></th><th>#</th><th>Line</th><th></th><th>Line coverage</th></tr></thead>
<tbody>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line1"></a><code>1</code></td><td></td><td class="lightgray"><code>using&nbsp;RazeWinComTr.Areas.Admin.DbModel;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line2"></a><code>2</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line3"></a><code>3</code></td><td></td><td class="lightgray"><code>namespace&nbsp;RazeWinComTr.Areas.Admin.ViewModels.RzwSavings</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line4"></a><code>4</code></td><td></td><td class="lightgray"><code>{</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line5"></a><code>5</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;class&nbsp;RzwSavingsPlanViewModel</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line6"></a><code>6</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;{</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line7"></a><code>7</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;Id&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line8"></a><code>8</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string&nbsp;Name&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;string.Empty;</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line9"></a><code>9</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string&nbsp;TermType&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;string.Empty;</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line10"></a><code>10</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;TermDuration&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line11"></a><code>11</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;decimal&nbsp;InterestRate&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line12"></a><code>12</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;decimal&nbsp;MinRzwAmount&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line13"></a><code>13</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;decimal?&nbsp;MaxRzwAmount&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line14"></a><code>14</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;bool&nbsp;IsActive&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line15"></a><code>15</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;DisplayOrder&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line16"></a><code>16</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string?&nbsp;Description&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line17"></a><code>17</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;DateTime&nbsp;CreatedDate&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line18"></a><code>18</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;DateTime?&nbsp;ModifiedDate&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line19"></a><code>19</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line20"></a><code>20</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//&nbsp;Statistics</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line21"></a><code>21</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;TotalAccounts&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line22"></a><code>22</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;decimal&nbsp;TotalLockedRzw&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line23"></a><code>23</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;decimal&nbsp;TotalInterestPaid&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line24"></a><code>24</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line25"></a><code>25</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//&nbsp;Display&nbsp;properties</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits, 0 of 6 branches are covered)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line26"></a><code>26</code></td><td class="percentagebar percentagebar0"><i class="icon-fork"></i></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string&nbsp;TermDisplayText&nbsp;=&gt;&nbsp;TermType&nbsp;switch</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line27"></a><code>27</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line28"></a><code>28</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;Daily&quot;&nbsp;=&gt;&nbsp;$&quot;{TermDuration}&nbsp;Day(s)&quot;,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line29"></a><code>29</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;Monthly&quot;&nbsp;=&gt;&nbsp;$&quot;{TermDuration}&nbsp;Month(s)&quot;,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line30"></a><code>30</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;Yearly&quot;&nbsp;=&gt;&nbsp;$&quot;{TermDuration}&nbsp;Year(s)&quot;,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line31"></a><code>31</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;_&nbsp;=&gt;&nbsp;$&quot;{TermDuration}&nbsp;{TermType}&quot;</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line32"></a><code>32</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;};</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line33"></a><code>33</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line34"></a><code>34</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string&nbsp;InterestRateDisplayText&nbsp;=&gt;&nbsp;$&quot;{(InterestRate&nbsp;*&nbsp;100).ToString(&quot;N8&quot;).TrimEnd(&#39;0&#39;).TrimEnd(&#39;.&#39;)}%&quot;;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line35"></a><code>35</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="coverableline" title="Not covered (0 visits, 0 of 2 branches are covered)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line36"></a><code>36</code></td><td class="percentagebar percentagebar0"><i class="icon-fork"></i></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string&nbsp;StatusDisplayText&nbsp;=&gt;&nbsp;IsActive&nbsp;?&nbsp;&quot;Active&quot;&nbsp;:&nbsp;&quot;Inactive&quot;;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line37"></a><code>37</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="coverableline" title="Not covered (0 visits, 0 of 4 branches are covered)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line38"></a><code>38</code></td><td class="percentagebar percentagebar0"><i class="icon-fork"></i></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string&nbsp;MaxAmountDisplayText&nbsp;=&gt;&nbsp;MaxRzwAmount?.ToString(&quot;N8&quot;)&nbsp;??&nbsp;&quot;Unlimited&quot;;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line39"></a><code>39</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line40"></a><code>40</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;static&nbsp;RzwSavingsPlanViewModel&nbsp;FromEntity(RzwSavingsPlan&nbsp;entity)</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line41"></a><code>41</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line42"></a><code>42</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;new&nbsp;RzwSavingsPlanViewModel</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line43"></a><code>43</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line44"></a><code>44</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Id&nbsp;=&nbsp;entity.Id,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line45"></a><code>45</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Name&nbsp;=&nbsp;entity.Name,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line46"></a><code>46</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;TermType&nbsp;=&nbsp;entity.TermType,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line47"></a><code>47</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;TermDuration&nbsp;=&nbsp;entity.TermDuration,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line48"></a><code>48</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;InterestRate&nbsp;=&nbsp;entity.InterestRate,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line49"></a><code>49</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;MinRzwAmount&nbsp;=&nbsp;entity.MinRzwAmount,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line50"></a><code>50</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;MaxRzwAmount&nbsp;=&nbsp;entity.MaxRzwAmount,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line51"></a><code>51</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;IsActive&nbsp;=&nbsp;entity.IsActive,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line52"></a><code>52</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DisplayOrder&nbsp;=&nbsp;entity.DisplayOrder,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line53"></a><code>53</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Description&nbsp;=&nbsp;entity.Description,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line54"></a><code>54</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CreatedDate&nbsp;=&nbsp;entity.CreatedDate,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line55"></a><code>55</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ModifiedDate&nbsp;=&nbsp;entity.ModifiedDate</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line56"></a><code>56</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;};</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line57"></a><code>57</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line58"></a><code>58</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line59"></a><code>59</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;RzwSavingsPlan&nbsp;ToEntity()</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line60"></a><code>60</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line61"></a><code>61</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;new&nbsp;RzwSavingsPlan</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line62"></a><code>62</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line63"></a><code>63</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Id&nbsp;=&nbsp;Id,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line64"></a><code>64</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Name&nbsp;=&nbsp;Name,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line65"></a><code>65</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;TermType&nbsp;=&nbsp;TermType,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line66"></a><code>66</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;TermDuration&nbsp;=&nbsp;TermDuration,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line67"></a><code>67</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;InterestRate&nbsp;=&nbsp;InterestRate,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line68"></a><code>68</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;MinRzwAmount&nbsp;=&nbsp;MinRzwAmount,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line69"></a><code>69</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;MaxRzwAmount&nbsp;=&nbsp;MaxRzwAmount,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line70"></a><code>70</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;IsActive&nbsp;=&nbsp;IsActive,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line71"></a><code>71</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DisplayOrder&nbsp;=&nbsp;DisplayOrder,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line72"></a><code>72</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Description&nbsp;=&nbsp;Description,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line73"></a><code>73</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CreatedDate&nbsp;=&nbsp;CreatedDate,</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line74"></a><code>74</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ModifiedDate&nbsp;=&nbsp;ModifiedDate</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line75"></a><code>75</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;};</code></td></tr>
<tr class="coverableline" title="Not covered (0 visits)" data-coverage="{'AllTestMethods': {'VC': '0', 'LVS': 'red'}}"><td class="red">&nbsp;</td><td class="leftmargin rightmargin right">0</td><td class="rightmargin right"><a id="file0_line76"></a><code>76</code></td><td></td><td class="lightred"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line77"></a><code>77</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;}</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line78"></a><code>78</code></td><td></td><td class="lightgray"><code>}</code></td></tr>
</tbody>
</table>
</div>
<div class="footer">Generated by: ReportGenerator 5.4.8.0<br />6/29/2025 - 2:31:29 PM<br /><a href="https://github.com/danielpalme/ReportGenerator">GitHub</a> | <a href="https://reportgenerator.io">reportgenerator.io</a></div></div>
<div class="containerright">
<div class="containerrightfixed">
<h1>Methods/Properties</h1>
<a href="#file0_line7" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_Id()"><i class="icon-wrench"></i>get_Id()</a><br />
<a href="#file0_line8" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_Name()"><i class="icon-wrench"></i>get_Name()</a><br />
<a href="#file0_line9" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_TermType()"><i class="icon-wrench"></i>get_TermType()</a><br />
<a href="#file0_line10" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_TermDuration()"><i class="icon-wrench"></i>get_TermDuration()</a><br />
<a href="#file0_line11" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_InterestRate()"><i class="icon-wrench"></i>get_InterestRate()</a><br />
<a href="#file0_line12" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_MinRzwAmount()"><i class="icon-wrench"></i>get_MinRzwAmount()</a><br />
<a href="#file0_line13" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_MaxRzwAmount()"><i class="icon-wrench"></i>get_MaxRzwAmount()</a><br />
<a href="#file0_line14" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_IsActive()"><i class="icon-wrench"></i>get_IsActive()</a><br />
<a href="#file0_line15" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_DisplayOrder()"><i class="icon-wrench"></i>get_DisplayOrder()</a><br />
<a href="#file0_line16" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_Description()"><i class="icon-wrench"></i>get_Description()</a><br />
<a href="#file0_line17" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_CreatedDate()"><i class="icon-wrench"></i>get_CreatedDate()</a><br />
<a href="#file0_line18" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_ModifiedDate()"><i class="icon-wrench"></i>get_ModifiedDate()</a><br />
<a href="#file0_line21" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_TotalAccounts()"><i class="icon-wrench"></i>get_TotalAccounts()</a><br />
<a href="#file0_line22" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_TotalLockedRzw()"><i class="icon-wrench"></i>get_TotalLockedRzw()</a><br />
<a href="#file0_line23" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_TotalInterestPaid()"><i class="icon-wrench"></i>get_TotalInterestPaid()</a><br />
<a href="#file0_line26" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_TermDisplayText()"><i class="icon-wrench"></i>get_TermDisplayText()</a><br />
<a href="#file0_line34" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_InterestRateDisplayText()"><i class="icon-wrench"></i>get_InterestRateDisplayText()</a><br />
<a href="#file0_line36" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_StatusDisplayText()"><i class="icon-wrench"></i>get_StatusDisplayText()</a><br />
<a href="#file0_line38" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - get_MaxAmountDisplayText()"><i class="icon-wrench"></i>get_MaxAmountDisplayText()</a><br />
<a href="#file0_line41" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - FromEntity(RazeWinComTr.Areas.Admin.DbModel.RzwSavingsPlan)"><i class="icon-cube"></i>FromEntity(RazeWinComTr.Areas.Admin.DbModel.RzwSavingsPlan)</a><br />
<a href="#file0_line60" class="navigatetohash percentagebar percentagebar0" title="Line coverage: 0% - ToEntity()"><i class="icon-cube"></i>ToEntity()</a><br />
<br/></div>
</div></div>
<script type="text/javascript">
/* <![CDATA[ */
(function() {
    var url = window.location.href;
    var startOfQueryString = url.indexOf('?');
    var queryString = startOfQueryString > -1 ? url.substr(startOfQueryString) : '';

    if (startOfQueryString > -1) {
        var i = 0, href= null;
        var css = document.getElementsByTagName('link');

        for (i = 0; i < css.length; i++) {
            if (css[i].getAttribute('rel') !== 'stylesheet') {
            continue;
            }

            href = css[i].getAttribute('href');

            if (href) {
            css[i].setAttribute('href', href + queryString);
            }
        }

        var links = document.getElementsByTagName('a');

        for (i = 0; i < links.length; i++) {
            href = links[i].getAttribute('href');

            if (href
                && !href.startsWith('http://')
                && !href.startsWith('https://')
                && !href.startsWith('#')
                && href.indexOf('?') === -1) {
            links[i].setAttribute('href', href + queryString);
            }
        }
    }

    var newScript = document.createElement('script');
    newScript.src = 'class.js' + queryString;
    document.getElementsByTagName('body')[0].appendChild(newScript);
})();
/* ]]> */ 
</script></body></html>